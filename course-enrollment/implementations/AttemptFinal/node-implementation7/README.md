# University Course Registration & Enrollment API

A comprehensive Node.js API implementation for managing university course registration and enrollment systems, built according to the Product Requirements Document (PRD).

## Features

- **Term Management**: Create, open, close, and conclude academic terms
- **Course Management**: Create, publish, and cancel courses with delivery modes
- **Enrollment System**: Student enrollment with automatic waitlist handling
- **Ledger System**: Seat availability and tuition balance tracking
- **Role-Based Access Control**: Student, Professor, and Registrar roles
- **Business Rules**: Credit limits, drop limits, course capacity enforcement
- **Automatic Waitlist Promotion**: FIFO promotion when seats become available

## Installation

```bash
npm install
```

## Running the Server

```bash
# Development mode with auto-reload
npm run dev

# Production mode
npm start
```

The server will start on port 3000 by default.

## Authentication

All requests require authentication headers:
- `X-User-ID`: User's UUID
- `X-User-Role`: User role (STUDENT, PROFESSOR, or REGISTRAR)

## API Endpoints

### Term Management
- `POST /terms` - Create a new academic term
- `GET /terms/{termId}` - Get term details
- `PATCH /terms/{termId}:open-registration` - Open enrollment
- `PATCH /terms/{termId}:close-registration` - Close enrollment
- `PATCH /terms/{termId}:conclude` - Conclude term

### Course Management
- `POST /terms/{termId}/courses` - Create a course
- `GET /terms/{termId}/courses` - List courses
- `GET /terms/{termId}/courses/{courseId}` - Get course details
- `PATCH /terms/{termId}/courses/{courseId}:publish` - Publish course
- `PATCH /terms/{termId}/courses/{courseId}:cancel` - Cancel course

### Enrollment Management
- `POST /terms/{termId}/courses/{courseId}/enrollments` - Enroll in course
- `GET /terms/{termId}/courses/{courseId}/enrollments` - List enrollments
- `GET /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}` - Get enrollment
- `PATCH /terms/{termId}/courses/{courseId}/enrollments/{enrollmentId}:drop` - Drop enrollment

### Payment Processing
- `POST /terms/{termId}/students/{studentId}:pay` - Record payment

## Business Rules

- **Credit Limit**: Students limited to 18 credits per term
- **Course Limit**: Professors limited to 5 courses per term
- **Drop Limit**: Students limited to 3 drops per term (with $50 penalty on 3rd drop)
- **Waitlist**: Automatic FIFO promotion when seats become available
- **State Transitions**: Enforced lifecycle states for terms, courses, and enrollments

## Response Format

All responses follow a standard envelope format:

### Success Response
```json
{
  "meta": {
    "api_request_id": "req_EXAMPLE123",
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "object",
  "data": { }
}
```

### Error Response
```json
{
  "meta": {
    "api_request_id": "req_EXAMPLE123", 
    "api_request_timestamp": "2025-05-24T10:30:00.123Z"
  },
  "response_type": "error",
  "data": {
    "error_id": "ERR_EXAMPLE",
    "message": "Human-readable error description"
  }
}
```

## Example Usage

### Create a Term (Registrar only)
```bash
curl -X POST http://localhost:3000/terms \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440000" \
  -H "X-User-Role: REGISTRAR" \
  -H "Content-Type: application/json" \
  -d '{"name": "Fall 2025"}'
```

### Create a Course (Professor or Registrar)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440001" \
  -H "X-User-Role: PROFESSOR" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "CS101",
    "title": "Introduction to Computer Science",
    "description": "Basic programming concepts",
    "credits": 3,
    "capacity": 30,
    "delivery_mode": "IN_PERSON",
    "location": "Room 101"
  }'
```

### Enroll in Course (Student)
```bash
curl -X POST http://localhost:3000/terms/{termId}/courses/{courseId}/enrollments \
  -H "X-User-ID: 550e8400-e29b-41d4-a716-446655440002" \
  -H "X-User-Role: STUDENT" \
  -H "Content-Type: application/json"
```

## Data Storage

This implementation uses in-memory data storage for simplicity. In a production environment, you would replace this with a proper database system.

## Error Codes

The API implements comprehensive error handling with specific error codes for different failure scenarios. See the PRD for the complete error code catalogue.