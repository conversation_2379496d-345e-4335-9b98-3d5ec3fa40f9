import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen,
  createTermPlanning,
  createTermEnrollmentClosed,
  getSuccessData,
  UserRole,
  EnrollmentState,
  TermState,
  CourseState,
  ApiErrorId,
  DeliveryMode,
  CreateCoursePayload,
  validateErrorEnvelope
} from './helpers';

describe('TC-122: Drops rejected when term not in ENROLLMENT_OPEN', () => {
  it('should reject drop attempt when term is ENROLLMENT_CLOSED', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
    const courseData: CreateCoursePayload = {
      code: 'CS101',
      title: 'Intro to CS',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);

    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);
    expect(enrollment.state).toBe(EnrollmentState.ENROLLED);

    const termResp = await apiClient.getTerm(term.id, registrarHeaders);
    const termData = getSuccessData(termResp);
    await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);

    const dropResp = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(dropResp, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
  });
});

describe('TC-123: Admin drops don\'t count toward student drop limit', () => {
  it('should allow student to drop after 3 admin drops without penalty', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = 'b1111111-2222-3333-4444-555555555555';
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
    const professor1Id = 'a1111111-2222-3333-4444-555555555555';
    const professor1Headers = createHeaders(professor1Id, UserRole.PROFESSOR);
    const professor2Id = 'a1111111-2222-3333-4444-666666666666';
    const professor2Headers = createHeaders(professor2Id, UserRole.PROFESSOR);

    for (let i = 0; i < 3; i++) {
      const courseData: CreateCoursePayload = {
        code: `GP${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Gap Test Admin Drop Course ${i + 1}`,
        credits: 1,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${500 + i}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professor1Headers);
      const course = getSuccessData(courseResp);

      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor1Headers);

      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);

      await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professor1Headers
      );
    }

    const courseData: CreateCoursePayload = {
      code: 'GP401',
      title: 'Gap Test Student Drop Course',
      credits: 1,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap2401'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professor2Headers);
    const course = getSuccessData(courseResp);

    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor2Headers);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);

    const dropResp = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );

    expect(dropResp.status).toBe(200);
    const droppedEnrollment = getSuccessData(dropResp);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    for (let i = 0; i < 2; i++) {
      const cd: CreateCoursePayload = {
        code: `GP5${String(i + 1).padStart(2, '0')}`,
        title: `Gap Test Student Drop Course ${i + 2}`,
        credits: 1,
        capacity: 10,
        delivery_mode: DeliveryMode.HYBRID,
        location: `Room 60${i}`,
        online_link: `https://example.com/gap25${i + 1}`
      };
      const cr = await apiClient.createCourse(term.id, cd, professor2Headers);
      const c = getSuccessData(cr);
      await apiClient.publishCourse(term.id, c.id, { revision: c.revision }, professor2Headers);
      const er = await apiClient.createEnrollment(term.id, c.id, {}, studentHeaders);
      const e = getSuccessData(er);

      if (i === 1) {
        const dr = await apiClient.dropEnrollment(
          term.id,
          c.id,
          e.id,
          { revision: e.revision },
          studentHeaders
        );
        expect(dr.status).toBe(200);
      } else {
        await apiClient.dropEnrollment(
          term.id,
          c.id,
          e.id,
          { revision: e.revision },
          studentHeaders
        );
      }
    }
  });
});

describe('TC-124: Full refund should result in negative balance when student has paid partially', () => {
  it('should provide full course refund even if balance goes negative', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = 'b2222222-3333-4444-5555-666666666666';
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
    const uniqueProfessorId = 'a2222222-3333-4444-5555-666666666666';
    const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'GAP301',
      title: 'Gap Test Finance',
      credits: 4,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap301'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);

    await apiClient.makePayment(term.id, uniqueStudentId, { amount: 38000 }, studentHeaders);

    await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, studentHeaders);

    const paymentResp = await apiClient.makePayment(term.id, uniqueStudentId, { amount: 1000 }, studentHeaders);
    expect(paymentResp.status).toBe(200);
    const paymentData = getSuccessData(paymentResp);

    expect(paymentData.new_balance).toBe(-37000);
  });
});

describe('TC-125: Confirm professor course-limit ignores CANCELLED and COMPLETED courses', () => {
  it('should allow professor to create a new course after cancelling one of their 5 courses', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueProfessorId = 'a3333333-4444-5555-6666-777777777777';
    const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);

    for (let i = 0; i < 5; i++) {
      const courseData: CreateCoursePayload = {
        code: `GPC${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Gap Test Course ${i + 1}`,
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${101 + i}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      expect(courseResp.status).toBe(201);
    }

    const coursesResp = await apiClient.listCourses(term.id, {}, professorHeaders);
    const courses = getSuccessData(coursesResp);
    const courseToCancel = courses[0];
    await apiClient.cancelCourse(term.id, courseToCancel.id, { revision: courseToCancel.revision }, professorHeaders);

    const newCourseData: CreateCoursePayload = {
      code: 'GPC601',
      title: 'Gap Test New Course',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap4new'
    };
    const newCourseResp = await apiClient.createCourse(term.id, newCourseData, professorHeaders);
    expect(newCourseResp.status).toBe(201);
  });
});

describe('TC-126: Validate strict unknown-field rejection on enrollment creation', () => {
  it('should reject enrollment with unknown fields', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'SEC101',
      title: 'Security',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, { student_id: TEST_USERS.STUDENT_A.id, foo: 'bar' } as any, studentHeaders);
    validateErrorEnvelope(enrollResp, { expectedErrorId: ApiErrorId.ERR_UNKNOWN_FIELD });
  });
});

describe('TC-127: Professor may NOT drop a student after registration is closed', () => {
  it('should reject drop attempt by professor when term is ENROLLMENT_CLOSED', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'CS101',
      title: 'Intro to CS',
      credits: 3,
      capacity: 1,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);

    const termResp = await apiClient.getTerm(term.id, registrarHeaders);
    const termData = getSuccessData(termResp);
    await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);

    const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, professorHeaders);
    validateErrorEnvelope(dropResp, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
  });
});

describe('TC-128: Even Registrar cannot drop once the term is CONCLUDED', () => {
  it('should reject drop attempt by registrar when term is CONCLUDED', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'CS101',
      title: 'Intro to CS',
      credits: 3,
      capacity: 1,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);

    let termResp = await apiClient.getTerm(term.id, registrarHeaders);
    let termData = getSuccessData(termResp);
    await apiClient.closeTermRegistration(term.id, { revision: termData.revision }, registrarHeaders);
    termResp = await apiClient.getTerm(term.id, registrarHeaders);
    termData = getSuccessData(termResp);
    await apiClient.concludeTerm(term.id, { revision: termData.revision }, registrarHeaders);

    const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, registrarHeaders);
    validateErrorEnvelope(dropResp, { expectedErrorId: ApiErrorId.ERR_ENROLLMENT_WRONG_STATE });
  });
});

describe('TC-129: Penalty fee applied exactly once on the third self-initiated drop', () => {
  it('should apply penalty fee on the 3rd self-initiated drop', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = 'b4444444-5555-6666-7777-888888888888';
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
    const uniqueProfessorId = 'a4444444-5555-6666-7777-888888888888';
    const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);

    const courses = [];
    for (let i = 0; i < 3; i++) {
      const courseData: CreateCoursePayload = {
        code: `GP${i + 1}${String(i + 10).padStart(2, '0')}`,
        title: `Gap Test Penalty Course ${i + 1}`,
        credits: 1,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${700 + i}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      courses.push(course);
    }

    let enrollResp = await apiClient.createEnrollment(term.id, courses[0].id, {}, studentHeaders);
    let enrollment = getSuccessData(enrollResp);
    await apiClient.dropEnrollment(term.id, courses[0].id, enrollment.id, { revision: enrollment.revision }, studentHeaders);

    enrollResp = await apiClient.createEnrollment(term.id, courses[1].id, {}, studentHeaders);
    enrollment = getSuccessData(enrollResp);
    await apiClient.dropEnrollment(term.id, courses[1].id, enrollment.id, { revision: enrollment.revision }, professorHeaders);

    enrollResp = await apiClient.createEnrollment(term.id, courses[2].id, {}, studentHeaders);
    enrollment = getSuccessData(enrollResp);
    await apiClient.dropEnrollment(term.id, courses[2].id, enrollment.id, { revision: enrollment.revision }, studentHeaders);

    const courseData: CreateCoursePayload = {
      code: 'GP901',
      title: 'Gap Test Penalty Course 4',
      credits: 1,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap9401'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
    enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    enrollment = getSuccessData(enrollResp);

    const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, studentHeaders);
    expect(dropResp.status).toBe(200);

    const paymentResp = await apiClient.makePayment(term.id, uniqueStudentId, { amount: 1 }, studentHeaders);
    expect(paymentResp.status).toBe(200);
    const paymentData = getSuccessData(paymentResp);

    expect(paymentData.new_balance).toBe(4999);

    const course4Data: CreateCoursePayload = {
      code: 'GP902',
      title: 'Gap Test Penalty Course 5',
      credits: 1,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 800'
    };
    const course4Resp = await apiClient.createCourse(term.id, course4Data, professorHeaders);
    const course4 = getSuccessData(course4Resp);
    await apiClient.publishCourse(term.id, course4.id, { revision: course4.revision }, professorHeaders);
    const enroll4Resp = await apiClient.createEnrollment(term.id, course4.id, {}, studentHeaders);
    const enrollment4 = getSuccessData(enroll4Resp);

    const drop4Resp = await apiClient.dropEnrollment(term.id, course4.id, enrollment4.id, { revision: enrollment4.revision }, studentHeaders);
    validateErrorEnvelope(drop4Resp, { expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS });
  });
});

describe('TC-130: Course cancellation should provide full refund even if balance goes negative', () => {
  it('should provide full course refund when course is cancelled, even if balance goes negative', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueStudentId = 'b5555555-6666-7777-8888-999999999999';
    const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
    const uniqueProfessorId = 'a5555555-6666-7777-8888-999999999999';
    const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'GAP101',
      title: 'Gap Test Advanced Finance',
      credits: 4,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap1001'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const updatedCourseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
    const updatedCourse = getSuccessData(updatedCourseResp);

    await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    await apiClient.makePayment(term.id, uniqueStudentId, { amount: 39000 }, studentHeaders);

    await apiClient.cancelCourse(term.id, course.id, { revision: updatedCourse.revision }, professorHeaders);

    const paymentResp = await apiClient.makePayment(term.id, uniqueStudentId, { amount: 1000 }, studentHeaders);
    expect(paymentResp.status).toBe(200);
    const paymentData = getSuccessData(paymentResp);

    expect(paymentData.new_balance).toBe(-38000);
  });
});

describe('TC-131: After cancelling one of five active courses, professor may create a new one', () => {
  it('should allow professor to create a new course after cancelling one of their 5 courses', async () => {
    const { term } = await createTermEnrollmentOpen();
    const uniqueProfessorId = 'c1111111-2222-3333-4444-666666666666';
    const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    await apiClient.listCourses(term.id, {}, professorHeaders);

    for (let i = 0; i < 5; i++) {
      const courseData: CreateCoursePayload = {
        code: `GPD${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Gap Test Course ${i + 1}`,
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${101 + i}`,
        professor_id: uniqueProfessorId
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, registrarHeaders);
      expect(courseResp.status).toBe(201);
    }

    const coursesResp = await apiClient.listCourses(term.id, {}, professorHeaders);
    const courses = getSuccessData(coursesResp);
    const courseToCancel = courses[0];
    await apiClient.cancelCourse(term.id, courseToCancel.id, { revision: courseToCancel.revision }, professorHeaders);

    const newCourseData: CreateCoursePayload = {
      code: 'GPD601',
      title: 'Gap Test New Course',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/gap4new'
    };
    const newCourseResp = await apiClient.createCourse(term.id, newCourseData, professorHeaders);
    expect(newCourseResp.status).toBe(201);
  });
});

describe('TC-132: Payment endpoint must reject extraneous fields', () => {
  it('should reject payment with unknown fields', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const paymentResp = await apiClient.makePayment(term.id, TEST_USERS.STUDENT_A.id, { amount: 5000, hack: 'oops' } as any, studentHeaders);
    validateErrorEnvelope(paymentResp, { expectedErrorId: ApiErrorId.ERR_UNKNOWN_FIELD });
  });
});

describe('TC-133: Enrollment drop endpoint must reject unknown body members', () => {
  it('should reject drop with unknown fields', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'SEC101',
      title: 'Security',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 101'
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    const enrollment = getSuccessData(enrollResp);

    const dropResp = await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision, extra: true } as any, studentHeaders);
    validateErrorEnvelope(dropResp, { expectedErrorId: ApiErrorId.ERR_UNKNOWN_FIELD });
  });
});

describe('TC-134: Term state transition endpoints must reject unknown fields', () => {
  it('should reject open-registration with unknown fields', async () => {
    const { term } = await createTermPlanning();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.openTermRegistration(
      term.id,
      { revision: term.revision, extraField: 'invalid' } as any,
      registrarHeaders
    );


    const error = response.data as any;
    expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
  });

  it('should reject close-registration with unknown fields', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.closeTermRegistration(
      term.id,
      { revision: term.revision, unknownProp: 123 } as any,
      registrarHeaders
    );


    const error = response.data as any;
    expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
  });

  it('should reject conclude with unknown fields', async () => {
    const { term } = await createTermEnrollmentClosed();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.concludeTerm(
      term.id,
      { revision: term.revision, badField: true } as any,
      registrarHeaders
    );


    const error = response.data as any;
    expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
  });
});

describe('TC-135: Term state transitions must increment revision by 1', () => {
  it('should increment revision by 1 after successful open-registration', async () => {
    const { term } = await createTermPlanning();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const initialRevision = term.revision;

    const response = await apiClient.openTermRegistration(
      term.id,
      { revision: initialRevision },
      registrarHeaders
    );

    expect(response.status).toBe(200);
    const updatedTerm = getSuccessData(response);
    expect(updatedTerm.revision).toBe(initialRevision + 1);
    expect(updatedTerm.state).toBe(TermState.ENROLLMENT_OPEN);
  });

  it('should increment revision by 1 after successful close-registration', async () => {
    const { term } = await createTermEnrollmentOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const initialRevision = term.revision;

    const response = await apiClient.closeTermRegistration(
      term.id,
      { revision: initialRevision },
      registrarHeaders
    );

    expect(response.status).toBe(200);
    const updatedTerm = getSuccessData(response);
    expect(updatedTerm.revision).toBe(initialRevision + 1);
    expect(updatedTerm.state).toBe(TermState.ENROLLMENT_CLOSED);
  });

  it('should increment revision by 1 after successful conclude', async () => {
    const { term } = await createTermEnrollmentClosed();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const initialRevision = term.revision;

    const response = await apiClient.concludeTerm(
      term.id,
      { revision: initialRevision },
      registrarHeaders
    );

    expect(response.status).toBe(200);
    const updatedTerm = getSuccessData(response);
    expect(updatedTerm.revision).toBe(initialRevision + 1);
    expect(updatedTerm.state).toBe(TermState.CONCLUDED);
  });
});

describe('TC-137: Waitlist promotion credit limit bypass', () => {
  it('should NOT promote a waitlisted student if it would exceed their credit limit', async () => {
    const { term } = await createTermEnrollmentOpen();

    const student = 'b6666666-7777-8888-9999-aaaaaaaaaaaa';
    const studentHeaders = createHeaders(student, UserRole.STUDENT);
    const enrolledStudent = 'b7777777-8888-9999-aaaa-bbbbbbbbbbbb';
    const enrolledStudentHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
    const professor1 = 'a6666666-7777-8888-9999-aaaaaaaaaaaa';
    const professor1Headers = createHeaders(professor1, UserRole.PROFESSOR);
    const professor2 = 'a6666666-7777-8888-9999-bbbbbbbbbbbb';
    const professor2Headers = createHeaders(professor2, UserRole.PROFESSOR);

    const courses = [];
    for (let i = 0; i < 4; i++) {
      const credits = 4;
      const professorHeaders = i < 2 ? professor1Headers : professor2Headers;
      const courseData: CreateCoursePayload = {
        code: `WL${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Waitlist Test Course ${i + 1}`,
        credits: credits,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${800 + i}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      courses.push(course);
    }

    for (const course of courses) {
      await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
    }

    const fullCourseData: CreateCoursePayload = {
      code: 'WL501',
      title: 'Waitlist Test Full Course',
      credits: 3,
      capacity: 1,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/wl501'
    };
    const fullCourseResp = await apiClient.createCourse(term.id, fullCourseData, professor2Headers);
    const fullCourse = getSuccessData(fullCourseResp);
    await apiClient.publishCourse(term.id, fullCourse.id, { revision: fullCourse.revision }, professor2Headers);

    const enrollment1Resp = await apiClient.createEnrollment(term.id, fullCourse.id, {}, enrolledStudentHeaders);
    const enrollment1 = getSuccessData(enrollment1Resp);
    expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);

    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const enrollment2Resp = await apiClient.createEnrollment(
      term.id,
      fullCourse.id,
      { student_id: student },
      registrarHeaders
    );
    const enrollment2 = getSuccessData(enrollment2Resp);
    expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);

    await apiClient.dropEnrollment(term.id, fullCourse.id, enrollment1.id, { revision: enrollment1.revision }, enrolledStudentHeaders);

    const updatedEnrollmentResp = await apiClient.getEnrollment(term.id, fullCourse.id, enrollment2.id, studentHeaders);
    const updatedEnrollment = getSuccessData(updatedEnrollmentResp);

    expect(updatedEnrollment.state).toBe(EnrollmentState.WAITLISTED);

    const courseDetailsResp = await apiClient.getCourse(term.id, fullCourse.id, professor2Headers);
    const courseDetails = getSuccessData(courseDetailsResp);
    expect(courseDetails.available_seats).toBe(1);
  });
});

describe('TC-138: Waitlist promotion skips over-credit students', () => {
  it('should skip waitlisted students who would exceed credit limit and promote the next eligible', async () => {
    const { term } = await createTermEnrollmentOpen();

    const student1 = 'ba111111-2222-3333-4444-555555555555';
    const student1Headers = createHeaders(student1, UserRole.STUDENT);
    const student2 = 'ba222222-3333-4444-5555-666666666666';
    const student2Headers = createHeaders(student2, UserRole.STUDENT);
    const enrolledStudent = 'ba333333-4444-5555-6666-777777777777';
    const enrolledStudentHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
    const professor1 = 'aa111111-2222-3333-4444-555555555555';
    const professor1Headers = createHeaders(professor1, UserRole.PROFESSOR);
    const professor2 = 'aa222222-3333-4444-5555-666666666666';
    const professor2Headers = createHeaders(professor2, UserRole.PROFESSOR);
    const professor3 = 'aa333333-4444-5555-6666-777777777777';
    const professor3Headers = createHeaders(professor3, UserRole.PROFESSOR);

    for (let i = 0; i < 4; i++) {
      const credits = 4;
      const professorHeaders = i < 2 ? professor1Headers : professor2Headers;
      const courseData: CreateCoursePayload = {
        code: `MX${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Max Credit Course ${i + 1}`,
        credits: credits,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: `Room ${860 + i}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);
      await apiClient.createEnrollment(term.id, course.id, {}, student1Headers);
    }

    for (let i = 0; i < 3; i++) {
      const courseData: CreateCoursePayload = {
        code: `LT${i + 1}${String(i + 1).padStart(2, '0')}`,
        title: `Light Credit Course ${i + 1}`,
        credits: 4,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: `https://example.com/lt${i + 1}`
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professor3Headers);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor3Headers);
      await apiClient.createEnrollment(term.id, course.id, {}, student2Headers);
    }

    const contestedCourseData: CreateCoursePayload = {
      code: 'CNT101',
      title: 'Contested Course',
      credits: 3,
      capacity: 1,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/cnt101'
    };
    const contestedCourseResp = await apiClient.createCourse(term.id, contestedCourseData, professor3Headers);
    const contestedCourse = getSuccessData(contestedCourseResp);
    await apiClient.publishCourse(term.id, contestedCourse.id, { revision: contestedCourse.revision }, professor3Headers);

    const enrollment0Resp = await apiClient.createEnrollment(term.id, contestedCourse.id, {}, enrolledStudentHeaders);
    const enrollment0 = getSuccessData(enrollment0Resp);
    expect(enrollment0.state).toBe(EnrollmentState.ENROLLED);

    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const enrollment1Resp = await apiClient.createEnrollment(
      term.id,
      contestedCourse.id,
      { student_id: student1 },
      registrarHeaders
    );
    const enrollment1 = getSuccessData(enrollment1Resp);
    expect(enrollment1.state).toBe(EnrollmentState.WAITLISTED);

    const enrollment2Resp = await apiClient.createEnrollment(term.id, contestedCourse.id, {}, student2Headers);
    const enrollment2 = getSuccessData(enrollment2Resp);
    expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);

    await apiClient.dropEnrollment(term.id, contestedCourse.id, enrollment0.id, { revision: enrollment0.revision }, enrolledStudentHeaders);

    const updatedEnrollment1Resp = await apiClient.getEnrollment(term.id, contestedCourse.id, enrollment1.id, student1Headers);
    const updatedEnrollment1 = getSuccessData(updatedEnrollment1Resp);
    expect(updatedEnrollment1.state).toBe(EnrollmentState.WAITLISTED);

    const updatedEnrollment2Resp = await apiClient.getEnrollment(term.id, contestedCourse.id, enrollment2.id, student2Headers);
    const updatedEnrollment2 = getSuccessData(updatedEnrollment2Resp);
    expect(updatedEnrollment2.state).toBe(EnrollmentState.ENROLLED);
  });
});

describe('TC-139: Incomplete tuition refund for cancelled course', () => {
  it('should expose that refunds are capped at current balance instead of full course cost', async () => {
    const { term } = await createTermEnrollmentOpen();

    const student = 'b8888888-9999-aaaa-bbbb-cccccccccccc';
    const studentHeaders = createHeaders(student, UserRole.STUDENT);
    const professor = 'a8888888-9999-aaaa-bbbb-cccccccccccc';
    const professorHeaders = createHeaders(professor, UserRole.PROFESSOR);

    const courseData: CreateCoursePayload = {
      code: 'REF101',
      title: 'Refund Test Course',
      credits: 3,
      capacity: 10,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 900',
      professor_id: professor
    };
    const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
    const course = getSuccessData(courseResp);
    await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

    const updatedCourseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
    const updatedCourse = getSuccessData(updatedCourseResp);

    await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    await apiClient.makePayment(term.id, student, { amount: 10000 }, studentHeaders);

    await apiClient.cancelCourse(term.id, course.id, { revision: updatedCourse.revision }, professorHeaders);

    const paymentResp = await apiClient.makePayment(term.id, student, { amount: 1 }, studentHeaders);
    expect(paymentResp.status).toBe(200);
    const paymentData = getSuccessData(paymentResp);
    expect(paymentData.new_balance).toBe(-9999);
  });
});